{"@timestamp":"2025-08-04T07:48:08.191694+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"2ddb9ade7a4e4089f59b8f6691cbfb9a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\Auth\LoginController::login(Line: 103) - User logged in successfully with email id: <EMAIL> []"}
{"@timestamp":"2025-08-04T07:48:10.346312+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"852ea2ffd84d61fcfd42cbeeb2cc8d90","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1090) - Dashboard Info Box population started. []"}
{"@timestamp":"2025-08-04T07:48:10.745282+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"852ea2ffd84d61fcfd42cbeeb2cc8d90","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1188) - Dashboard Information fetched :  {"total_sales_v1":"0.00","total_transactions_v1":"0","total_sales_v2":"0.00","total_transactions_v2":"0","total_monthly_sales_v1":"0.00","total_monthly_transactions_v1":"0","total_monthly_sales_v2":"0.00","total_monthly_transactions_v2":"0","new_enrollments_v1":"0","total_enrollments_v1":"35","new_enrollments_v2":"0","total_enrollments_v2":"539","avg_weekly_sales":"0.00","avg_weekly_transactions":"0","start_date":"08/03/2025","end_date":"07/28/2025","remotepay_daily_registration":0,"remotepay_monthly_registration":0,"total_monthly_admin_driven_not_posted_amount":"0.00","total_daily_admin_driven_not_posted_amount":"0.00","lite_users_today":0,"lite_users_monthly":0,"lite_to_standard_conversions_today":0,"lite_to_standard_conversions_monthly":0}"}
{"@timestamp":"2025-08-04T07:49:39.549544+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"080297c300f14bba3950faba89099d42","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\DataMigrationController::getMigrationLog(Line457) : Migration Log List fetched successfully []"}
{"@timestamp":"2025-08-04T07:49:43.428888+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"ed465f9eaa2c9eaf8c1c77a045a29b7c","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for HttpException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php] Line number[123] with Code [0] and  Message [The POST method is not supported for route api/getpetitions. Supported methods: GET, HEAD.] []"}
{"@timestamp":"2025-08-04T07:49:45.093228+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"1b1c379a01e6a300008ac5009dc4ddf9","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\Auth\LoginController::login(Line: 103) - User logged in successfully with email id: <EMAIL> []"}
{"@timestamp":"2025-08-04T07:49:45.820709+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"72af7e4369c880e9567f8f2217097a82","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1090) - Dashboard Info Box population started. []"}
{"@timestamp":"2025-08-04T07:49:45.861471+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"72af7e4369c880e9567f8f2217097a82","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1188) - Dashboard Information fetched :  {"total_sales_v1":"0.00","total_transactions_v1":"0","total_sales_v2":"0.00","total_transactions_v2":"0","total_monthly_sales_v1":"0.00","total_monthly_transactions_v1":"0","total_monthly_sales_v2":"0.00","total_monthly_transactions_v2":"0","new_enrollments_v1":"0","total_enrollments_v1":"35","new_enrollments_v2":"0","total_enrollments_v2":"539","avg_weekly_sales":"0.00","avg_weekly_transactions":"0","start_date":"08/03/2025","end_date":"07/28/2025","remotepay_daily_registration":0,"remotepay_monthly_registration":0,"total_monthly_admin_driven_not_posted_amount":"0.00","total_daily_admin_driven_not_posted_amount":"0.00","lite_users_today":0,"lite_users_monthly":0,"lite_to_standard_conversions_today":0,"lite_to_standard_conversions_monthly":0}"}
{"@timestamp":"2025-08-04T07:50:07.230262+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"f92b303395926cb81de4487c262c1093","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:05:24.925776+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"2767510a5e0c6b471f87568a70dfa80a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:05:50.068344+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"03d264650d3b4cac179c75615958dfcd","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\DataMigrationController::getMigrationLog(Line457) : Migration Log List fetched successfully []"}
{"@timestamp":"2025-08-04T09:06:01.237984+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"f6caa7a021ec87f71827a7ca431d3804","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:13:31.259573+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"6262ce2cf265f97b06a2f4e90253b576","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:18:22.087575+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"95bf106b0c8e3438974142caeef071f4","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for ValidationException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\app\\Http\\Controllers\\Controller.php] Line number[27] with Code [0] and  Message [The Corporate Color Field Is Required When Corporate Vanity Url / Logo Url / Display Name / Corporate Url Is Present.] []"}
{"@timestamp":"2025-08-04T09:18:42.633521+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"875f86327e9b0e8f9df6130f0b90c481","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for ValidationException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\app\\Http\\Controllers\\Controller.php] Line number[27] with Code [0] and  Message [The Corporate Vanity Url Field Is Required When Corporate Color / Logo Url / Display Name / Corporate Url Is Present.] []"}
{"@timestamp":"2025-08-04T09:19:04.964700+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"37b862c9a67f98a15d618c18678e8737","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:36:50.311885+00:00","V":"V-74b0c23","EC2":"Local-Server","IP":"127.0.0.1","TID":"b0fe5b8a23666171cd71f98f39f7d25b","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:48:57.256076+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"a0be381a911d123c2e0610801dd5b1bb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 151) - File uploaded to S3, path:  []"}
{"@timestamp":"2025-08-04T09:48:57.257504+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"a0be381a911d123c2e0610801dd5b1bb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 155) - putFile returned empty path []"}
{"@timestamp":"2025-08-04T09:51:46.479461+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"82a7589f5486078ef8ce1d12a1c7e644","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:52:00.219799+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"e35bd62ffdd1173f4790407d500f601c","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:58:43.610143+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"50f6da9c58163bd26971159aaf664f1a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T10:23:19.985731+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"9ca054e1c546666c8e045544af597963","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T10:56:42.052037+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"33c0fac6070881da7e6e33e7646fa418","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T11:00:18.170267+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"0f6d64addc8a18804114f369cf29dd96","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for ValidationException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\app\\Http\\Controllers\\Controller.php] Line number[27] with Code [0] and  Message [The Corporate Vanity Url Field Is Required When Corporate Color / Logo Url / Display Name / Corporate Url Is Present.] []"}
{"@timestamp":"2025-08-04T11:00:42.435598+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"replaceNonDefaultCorporateParents(Line: 1645): No non-default CP users found for the given store IDs. []"}
{"@timestamp":"2025-08-04T11:00:42.457988+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1493) - Updating Store Petition Points. []"}
{"@timestamp":"2025-08-04T11:00:42.576145+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1536) No pending, unassigned corporate-parent reward points found for Petition IDs:  []"}
{"@timestamp":"2025-08-04T11:00:43.033909+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"insertNotificationData(Line: 850) - email notification data inserted into database successfully. []"}
{"@timestamp":"2025-08-04T11:00:43.034135+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\EmailExecutor\EmailExecutorFactory::_sendMail(Line: 264) - Notification data inserted and sending data to mail function. []"}
{"@timestamp":"2025-08-04T11:00:47.998767+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"11789b809c317c9a7c38096f8b6d22d0","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::addCorporateParent(Line: 228) - User (Corporate Parent) Created Successfully and inserted in Database with User ID :  by User ID : 578fe7fcfb246aa88009794ec2581edf []"}
{"@timestamp":"2025-08-04T11:06:16.728499+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"replaceNonDefaultCorporateParents(Line: 1645): No non-default CP users found for the given store IDs. []"}
{"@timestamp":"2025-08-04T11:06:16.742727+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1493) - Updating Store Petition Points. []"}
{"@timestamp":"2025-08-04T11:06:16.777706+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1536) No pending, unassigned corporate-parent reward points found for Petition IDs:  []"}
{"@timestamp":"2025-08-04T11:06:16.858592+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"insertNotificationData(Line: 850) - email notification data inserted into database successfully. []"}
{"@timestamp":"2025-08-04T11:06:16.858810+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\EmailExecutor\EmailExecutorFactory::_sendMail(Line: 264) - Notification data inserted and sending data to mail function. []"}
{"@timestamp":"2025-08-04T11:06:19.600568+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"53447168cad130ca05e2ad0231cd95fb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::addCorporateParent(Line: 228) - User (Corporate Parent) Created Successfully and inserted in Database with User ID :  by User ID : 578fe7fcfb246aa88009794ec2581edf []"}
{"@timestamp":"2025-08-04T11:09:40.062039+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"replaceNonDefaultCorporateParents(Line: 1645): No non-default CP users found for the given store IDs. []"}
{"@timestamp":"2025-08-04T11:09:40.068307+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1493) - Updating Store Petition Points. []"}
{"@timestamp":"2025-08-04T11:09:40.095956+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"updateStorePetitionPoints(Line: 1536) No pending, unassigned corporate-parent reward points found for Petition IDs:  []"}
{"@timestamp":"2025-08-04T11:09:40.144030+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"insertNotificationData(Line: 850) - email notification data inserted into database successfully. []"}
{"@timestamp":"2025-08-04T11:09:40.144162+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\EmailExecutor\EmailExecutorFactory::_sendMail(Line: 264) - Notification data inserted and sending data to mail function. []"}
{"@timestamp":"2025-08-04T11:09:41.970143+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"22a0951cfc529c4af042b37c9a1cd4bf","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::addCorporateParent(Line: 228) - User (Corporate Parent) Created Successfully and inserted in Database with User ID :  by User ID : 578fe7fcfb246aa88009794ec2581edf []"}
{"@timestamp":"2025-08-04T11:11:12.369286+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"ba060a87d74bc1e1eb8ca8d7f91e026b","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 495) - Corporate Parent search started... []"}
{"@timestamp":"2025-08-04T11:11:12.388153+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"ba060a87d74bc1e1eb8ca8d7f91e026b","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::_getCorporateParentSearch(Line: 519) - Corporate Parent Search Started. []"}
{"@timestamp":"2025-08-04T11:11:12.420425+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"ba060a87d74bc1e1eb8ca8d7f91e026b","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 507) - Corporate Parent search started complete. []"}
{"@timestamp":"2025-08-04T11:11:14.658850+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"12eeaf9fc0e4365f35ad5207b7e9cb6e","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 495) - Corporate Parent search started... []"}
{"@timestamp":"2025-08-04T11:11:14.675128+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"12eeaf9fc0e4365f35ad5207b7e9cb6e","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::_getCorporateParentSearch(Line: 519) - Corporate Parent Search Started. []"}
{"@timestamp":"2025-08-04T11:11:14.700646+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"12eeaf9fc0e4365f35ad5207b7e9cb6e","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 507) - Corporate Parent search started complete. []"}
{"@timestamp":"2025-08-04T11:11:28.503432+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"7ad34877d13d6582023a753790e80aca","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 495) - Corporate Parent search started... []"}
{"@timestamp":"2025-08-04T11:11:28.516948+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"7ad34877d13d6582023a753790e80aca","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::_getCorporateParentSearch(Line: 519) - Corporate Parent Search Started. []"}
{"@timestamp":"2025-08-04T11:11:28.555450+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"7ad34877d13d6582023a753790e80aca","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::getAllCorporateParents(Line: 507) - Corporate Parent search started complete. []"}
{"@timestamp":"2025-08-04T11:11:47.875546+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"16b28a47272cce91b7b7faacbd7adf95","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"DEBUG","MESSAGE":"email does not match []"}
{"@timestamp":"2025-08-04T11:11:48.071418+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"16b28a47272cce91b7b7faacbd7adf95","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"insertNotificationData(Line: 850) - email notification data inserted into database successfully. []"}
{"@timestamp":"2025-08-04T11:11:48.071603+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"16b28a47272cce91b7b7faacbd7adf95","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\EmailExecutor\EmailExecutorFactory::_sendMail(Line: 264) - Notification data inserted and sending data to mail function. []"}
{"@timestamp":"2025-08-04T11:11:50.848155+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"16b28a47272cce91b7b7faacbd7adf95","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\UserController::editCorporateParent(Line: 435) - User (Corporate Parent) Updated Successfully User ID :  by User ID : 578fe7fcfb246aa88009794ec2581edf []"}
{"@timestamp":"2025-08-04T11:20:08.921861+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"83a9465bf489352b6bd2ca6489a2ab5f","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T11:35:49.278589+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"727e3842d225f966998c474fa29ff941","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::getAllManualReviewDetails(Line: 49) - Manual Review Details search started... []"}
{"@timestamp":"2025-08-04T11:35:49.329365+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"727e3842d225f966998c474fa29ff941","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::_getManualReviewSearch(Line: 71) - Manual Review Details Search Started. []"}
{"@timestamp":"2025-08-04T11:35:49.363934+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"727e3842d225f966998c474fa29ff941","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::getAllManualReviewDetails(Line: 59) - Manual Review Details search complete. []"}
{"@timestamp":"2025-08-04T11:35:52.222595+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"62a6ee27f0d6a29667917dcb0505ab47","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::getIdentityValidationDetails(Line: 139) - Getting identity validation details for manual review details with id: 7b963c1067e0d2e18fd7be6cbfd13710 []"}
{"@timestamp":"2025-08-04T11:35:52.233372+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"62a6ee27f0d6a29667917dcb0505ab47","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"getRiskScore(Line: 1323) - Getting consumer risk score started... []"}
{"@timestamp":"2025-08-04T11:35:52.282912+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"62a6ee27f0d6a29667917dcb0505ab47","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for error Exception Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php] Line number[825] with Code [42000] and  Message [SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,   ' at line 5 (Connection: mysql_ro, SQL: WITH combined_results AS (         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM registration_session_details rsd         JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id         WHERE rsd.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         UNION ALL         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM users u         JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id         WHERE u.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         )         SELECT risk_score         FROM combined_results         ORDER BY created_at DESC         LIMIT 1)] []"}
{"@timestamp":"2025-08-04T11:35:55.277324+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"10cc3ac09ee9f5dd8f8e4b3aaa71b4be","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::getIdentityValidationDetails(Line: 139) - Getting identity validation details for manual review details with id: 7b963c1067e0d2e18fd7be6cbfd13710 []"}
{"@timestamp":"2025-08-04T11:35:55.282751+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"10cc3ac09ee9f5dd8f8e4b3aaa71b4be","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"getRiskScore(Line: 1323) - Getting consumer risk score started... []"}
{"@timestamp":"2025-08-04T11:35:55.287110+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"10cc3ac09ee9f5dd8f8e4b3aaa71b4be","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for error Exception Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php] Line number[825] with Code [42000] and  Message [SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,   ' at line 5 (Connection: mysql_ro, SQL: WITH combined_results AS (         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM registration_session_details rsd         JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id         WHERE rsd.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         UNION ALL         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM users u         JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id         WHERE u.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         )         SELECT risk_score         FROM combined_results         ORDER BY created_at DESC         LIMIT 1)] []"}
{"@timestamp":"2025-08-04T11:46:07.614049+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"6ee95bebf687ecc7a1d7359426c8fb06","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ManualReviewController::getIdentityValidationDetails(Line: 139) - Getting identity validation details for manual review details with id: 7b963c1067e0d2e18fd7be6cbfd13710 []"}
{"@timestamp":"2025-08-04T11:46:07.623124+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"6ee95bebf687ecc7a1d7359426c8fb06","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"getRiskScore(Line: 1323) - Getting consumer risk score started... []"}
{"@timestamp":"2025-08-04T11:46:07.641355+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"6ee95bebf687ecc7a1d7359426c8fb06","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for error Exception Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php] Line number[825] with Code [42000] and  Message [SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,   ' at line 5 (Connection: mysql_ro, SQL: WITH combined_results AS (         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM registration_session_details rsd         JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id         WHERE rsd.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         UNION ALL         SELECT          CASE          WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)          THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,          cppdt.created_at         FROM users u         JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id         WHERE u.phone = 7656776557 AND cppdt.risk_score IS NOT NULL         )         SELECT risk_score         FROM combined_results         ORDER BY created_at DESC         LIMIT 1)] []"}
